#!/bin/bash
set -e

# Xóa PID file cũ nếu tồn tại
rm -f /var/run/squid/squid.pid

# Đ<PERSON><PERSON> b<PERSON><PERSON> thư mục cache tồn tại và có permissions đúng
mkdir -p /var/spool/squid /var/log/squid /var/run/squid
chown -R proxy:proxy /var/spool/squid /var/log/squid /var/run/squid

# Khởi tạo cache nếu chưa tồn tại
if [ ! -d "/var/spool/squid/00" ]; then
    echo "Initializing squid cache..."
    squid -z -f /etc/squid/squid.conf
fi

# Khởi động squid
echo "Starting squid proxy..."
exec squid -N -f /etc/squid/squid.conf
