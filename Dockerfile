# Sử dụng image Ubuntu mới nhất
FROM ubuntu:latest

# Cập nhật gói và cài đặt Squid, apache2-utils
RUN apt-get update && \
    apt-get install -y squid apache2-utils && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Tạo tệp mật khẩu (thay username và password bằng giá trị của bạn)
RUN htpasswd -b -c /etc/squid/passwd proxyuser securepassword123

# Sao lưu tệp cấu hình gốc
RUN mv /etc/squid/squid.conf /etc/squid/squid.conf.bak

# Sao chép tệp cấu hình Squid tùy chỉnh
COPY squid.conf /etc/squid/squid.conf

# Sao chép entrypoint script
COPY entrypoint.sh /usr/local/bin/entrypoint.sh

# Tạo các thư mục cần thiết và thiết lập permissions
RUN mkdir -p /var/spool/squid /var/log/squid /var/run/squid && \
    chown -R proxy:proxy /var/spool/squid /var/log/squid /var/run/squid /etc/squid && \
    chmod 755 /var/spool/squid /var/log/squid /var/run/squid && \
    chmod +x /usr/local/bin/entrypoint.sh

# Mở cổng mặc định của Squid
EXPOSE 3128

# Sử dụng entrypoint script
ENTRYPOINT ["/usr/local/bin/entrypoint.sh"]