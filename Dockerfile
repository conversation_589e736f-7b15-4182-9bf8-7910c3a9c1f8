# Sử dụng image Ubuntu mới nhất
FROM ubuntu:latest

# Cập nhật gói và cài đặt Squid, apache2-utils
RUN apt-get update && \
    apt-get install -y squid apache2-utils && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Sử dụng user proxy hi<PERSON><PERSON> c<PERSON> (UID/GID 13)
# User proxy đã tồn tại trong Ubuntu base image

# Tạo tệp mật khẩu (thay username và password bằng giá trị của bạn)
RUN htpasswd -b -c /etc/squid/passwd proxyuser securepassword123  # Thay đổi username và password

# Sao lưu tệp cấu hình gốc
RUN mv /etc/squid/squid.conf /etc/squid/squid.conf.bak

# Sao chép tệp cấu hình Squid tùy chỉnh
COPY squid.conf /etc/squid/squid.conf

# Sao chép entrypoint script
COPY entrypoint.sh /usr/local/bin/entrypoint.sh

# T<PERSON><PERSON> c<PERSON>c thư mục cần thiết và thiết lập permissions
RUN mkdir -p /var/spool/squid /var/log/squid /var/run/squid && \
    chown -R proxy:proxy /var/spool/squid /var/log/squid /var/run/squid /etc/squid && \
    chmod 755 /var/spool/squid /var/log/squid /var/run/squid && \
    chmod +x /usr/local/bin/entrypoint.sh

# Khởi tạo cache directory
RUN squid -z -f /etc/squid/squid.conf

# Mở cổng mặc định của Squid
EXPOSE 3128

# Chuyển sang user proxy
USER proxy

# Khởi động squid trực tiếp
CMD ["squid", "-N", "-f", "/etc/squid/squid.conf"]