# squid.conf

# Cấu hình user và group
cache_effective_user proxy
cache_effective_group proxy

# Định nghĩa ACL cho tất cả IP
acl all_ips src 0.0.0.0/0  # Cho phép tất cả IP
acl localhost src 127.0.0.1
acl SSL_ports port 443
acl Safe_ports port 80
acl Safe_ports port 443
acl CONNECT method CONNECT

# Xác thực người dùng
auth_param basic program /usr/lib/squid/basic_ncsa_auth /etc/squid/passwd
auth_param basic realm Proxy Authentication
auth_param basic casesensitive on
acl authenticated proxy_auth REQUIRED

# Chặn các cổng không an toàn
http_access deny !Safe_ports
http_access deny CONNECT !SSL_ports

# Cho phép truy cập
http_access allow localhost
http_access allow localnet authenticated
# Cho phép tất cả để test (c<PERSON> thể thay đổi sau)
http_access allow all

# Cổng proxy (c<PERSON> thể thay đổi từ 3128 sang cổng khác)
http_port 3128

# Ẩn thông tin máy chủ
via off
forwarded_for delete
httpd_suppress_version_string on
server_persistent_connections off

# Tắt thông tin chi tiết trong lỗi
strip_query_terms on

# Cấu hình cache directory
cache_dir ufs /var/spool/squid 100 16 256

# Cấu hình PID file
pid_filename /var/run/squid/squid.pid

# Giảm debug messages
debug_options ALL,1

# Cấu hình log
access_log /var/log/squid/access.log squid
cache_log /var/log/squid/cache.log

# Cấu hình memory và performance
cache_mem 64 MB
maximum_object_size_in_memory 512 KB
memory_replacement_policy lru